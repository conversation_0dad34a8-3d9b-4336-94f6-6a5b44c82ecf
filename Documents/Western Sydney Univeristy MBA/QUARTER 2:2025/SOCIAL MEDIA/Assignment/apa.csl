<?xml version="1.0" encoding="utf-8"?>
<style xmlns="http://purl.org/net/xbiblio/csl" class="in-text" version="1.0" demote-non-dropping-particle="never">
  <info>
    <title>American Psychological Association 7th edition</title>
    <title-short>APA</title-short>
    <id>http://www.zotero.org/styles/apa</id>
    <link href="http://www.zotero.org/styles/apa" rel="self"/>
    <link href="https://apastyle.apa.org/" rel="documentation"/>
    <author>
      <name><PERSON></name>
      <email><EMAIL></email>
    </author>
    <category citation-format="author-date"/>
    <category field="psychology"/>
    <category field="generic-base"/>
    <updated>2020-09-28T14:51:37+00:00</updated>
    <rights license="http://creativecommons.org/licenses/by-sa/3.0/">This work is licensed under a Creative Commons Attribution-ShareAlike 3.0 License</rights>
  </info>
  <locale xml:lang="en">
    <terms>
      <term name="editortranslator" form="short">
        <single>ed. &amp; trans.</single>
        <multiple>eds. &amp; trans.</multiple>
      </term>
      <term name="translator" form="short">trans.</term>
    </terms>
  </locale>
  <macro name="container-contributors">
    <choose>
      <if type="chapter paper-conference entry-dictionary entry-encyclopedia" match="any">
        <group delimiter=", " suffix=", ">
          <names variable="container-author" delimiter=", ">
            <name and="symbol" initialize-with=". " delimiter=", "/>
            <label form="short" prefix=" (" text-case="title" suffix=")"/>
          </names>
          <names variable="editor translator" delimiter=", ">
            <name and="symbol" initialize-with=". " delimiter=", "/>
            <label form="short" prefix=" (" text-case="title" suffix=")"/>
          </names>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="secondary-contributors">
    <choose>
      <if type="article-journal chapter paper-conference entry-dictionary entry-encyclopedia" match="none">
        <names variable="editor translator" delimiter=", " prefix=" (" suffix=")">
          <name and="symbol" initialize-with=". " delimiter=", "/>
          <label form="short" prefix=", " text-case="title"/>
        </names>
      </if>
    </choose>
  </macro>
  <macro name="author">
    <names variable="author">
      <name name-as-sort-order="all" and="symbol" sort-separator=", " initialize-with=". " delimiter=", " delimiter-precedes-last="always"/>
      <label form="short" prefix=" (" suffix=")" text-case="capitalize-first"/>
      <substitute>
        <names variable="editor"/>
        <names variable="translator"/>
        <choose>
          <if type="report">
            <text variable="publisher"/>
            <text macro="title"/>
          </if>
          <else>
            <text macro="title"/>
          </else>
        </choose>
      </substitute>
    </names>
  </macro>
  <macro name="author-short">
    <names variable="author">
      <name form="short" and="symbol" delimiter=", " initialize-with=". "/>
      <substitute>
        <names variable="editor"/>
        <names variable="translator"/>
        <choose>
          <if type="report">
            <text variable="publisher"/>
            <text variable="title" form="short" font-style="italic"/>
          </if>
          <else-if type="legal_case">
            <text variable="title" font-style="italic"/>
          </else-if>
          <else-if type="book graphic motion_picture song" match="any">
            <text variable="title" form="short" font-style="italic"/>
          </else-if>
          <else-if type="bill legislation" match="any">
            <text variable="title" form="short"/>
          </else-if>
          <else>
            <text variable="title" form="short" quotes="true"/>
          </else>
        </choose>
      </substitute>
    </names>
  </macro>
  <macro name="access">
    <choose>
      <if type="thesis report" match="any">
        <choose>
          <if variable="archive" match="any">
            <group>
              <text term="retrieved" text-case="capitalize-first" suffix=" "/>
              <text term="from" suffix=" "/>
              <text variable="archive" suffix="."/>
              <text variable="archive_location" prefix=" (" suffix=")"/>
            </group>
          </if>
          <else>
            <group>
              <text term="retrieved" text-case="capitalize-first" suffix=" "/>
              <text term="from" suffix=" "/>
              <text variable="URL"/>
            </group>
          </else>
        </choose>
      </if>
      <else>
        <choose>
          <if variable="DOI">
            <text variable="DOI" prefix="https://doi.org/"/>
          </if>
          <else>
            <choose>
              <if type="webpage">
                <group delimiter=" ">
                  <text term="retrieved" text-case="capitalize-first"/>
                  <group>
                    <date variable="accessed" form="text" suffix=", "/>
                  </group>
                  <text term="from"/>
                  <text variable="URL"/>
                </group>
              </if>
              <else>
                <group>
                  <text term="retrieved" text-case="capitalize-first" suffix=" "/>
                  <text term="from" suffix=" "/>
                  <text variable="URL"/>
                </group>
              </else>
            </choose>
          </else>
        </choose>
      </else>
    </choose>
  </macro>
  <macro name="title">
    <choose>
      <if type="report thesis book graphic motion_picture report song manuscript speech" match="any">
        <choose>
          <if variable="version" type="book" match="all">
            <text variable="title"/>
          </if>
          <else>
            <text variable="title" font-style="italic"/>
          </else>
        </choose>
      </if>
      <else-if variable="reviewed-author">
        <choose>
          <if variable="reviewed-title">
            <group delimiter=" ">
              <text variable="title"/>
              <group delimiter=", " prefix="[" suffix="]">
                <text variable="reviewed-title" font-style="italic" prefix="Review of "/>
                <names variable="reviewed-author" delimiter=", ">
                  <label form="verb-short" suffix=" "/>
                  <name and="symbol" initialize-with=". " delimiter=", "/>
                </names>
              </group>
            </group>
          </if>
          <else>
            <group delimiter=", " prefix="[" suffix="]">
              <text variable="title" font-style="italic" prefix="Review of "/>
              <names variable="reviewed-author" delimiter=", ">
                <label form="verb-short" suffix=" "/>
                <name and="symbol" initialize-with=". " delimiter=", "/>
              </names>
            </group>
          </else>
        </choose>
      </else-if>
      <else>
        <text variable="title"/>
      </else>
    </choose>
  </macro>
  <macro name="title-plus-extra">
    <text macro="title"/>
    <choose>
      <if type="report thesis" match="any">
        <group prefix=" (" suffix=")" delimiter=", ">
          <group delimiter=" ">
            <choose>
              <if variable="genre" match="any">
                <text variable="genre"/>
              </if>
              <else>
                <text variable="medium"/>
              </else>
            </choose>
            <text variable="number" prefix="No. "/>
          </group>
          <group delimiter=" ">
            <text term="version" text-case="capitalize-first"/>
            <text variable="version"/>
          </group>
          <text macro="edition"/>
        </group>
      </if>
      <else-if type="post-weblog webpage" match="any">
        <text variable="genre" prefix=" [" suffix="]"/>
      </else-if>
      <else-if type="graphic motion_picture report song" match="any">
        <group prefix=" [" suffix="]" delimiter=", ">
          <choose>
            <if variable="medium" match="any">
              <text variable="medium"/>
            </if>
            <else>
              <text variable="genre"/>
            </else>
          </choose>
          <text macro="edition"/>
        </group>
      </else-if>
      <else-if type="book manuscript" match="any">
        <group prefix=" (" suffix=")" delimiter=", ">
          <text macro="edition"/>
          <choose>
            <if variable="medium" match="any">
              <text variable="medium"/>
            </if>
          </choose>
        </group>
      </else-if>
      <else-if type="chapter">
        <choose>
          <if variable="medium" match="any">
            <text variable="medium" prefix=" [" suffix="]"/>
          </if>
        </choose>
      </else-if>
    </choose>
  </macro>
  <macro name="publisher">
    <choose>
      <if type="report" match="any">
        <group delimiter=": ">
          <text variable="publisher-place"/>
          <text variable="publisher"/>
        </group>
      </if>
      <else-if type="thesis" match="any">
        <group delimiter=", ">
          <text variable="publisher"/>
          <text variable="publisher-place"/>
        </group>
      </else-if>
      <else-if type="post-weblog webpage" match="none">
        <group delimiter=", ">
          <choose>
            <if variable="event version" type="speech motion_picture" match="none">
              <text variable="genre"/>
            </if>
          </choose>
          <choose>
            <if type="article-journal article-magazine" match="none">
              <group delimiter=": ">
                <choose>
                  <if variable="publisher-place">
                    <text variable="publisher-place"/>
                  </if>
                  <else>
                    <text variable="event-place"/>
                  </else>
                </choose>
                <text variable="publisher"/>
              </group>
            </if>
          </choose>
        </group>
      </else-if>
    </choose>
  </macro>
  <macro name="event">
    <choose>
      <if variable="container-title" match="none">
        <choose>
          <if variable="event">
            <choose>
              <if variable="genre" match="none">
                <text term="presented at" text-case="capitalize-first" suffix=" "/>
                <text variable="event"/>
              </if>
              <else>
                <group delimiter=" ">
                  <text variable="genre" text-case="capitalize-first"/>
                  <text term="presented at"/>
                  <text variable="event"/>
                </group>
              </else>
            </choose>
          </if>
          <else-if type="speech">
            <text variable="genre" text-case="capitalize-first"/>
          </else-if>
        </choose>
      </if>
    </choose>
  </macro>
  <macro name="issued">
    <choose>
      <if type="bill legal_case legislation" match="none">
        <choose>
          <if variable="issued">
            <group prefix=" (" suffix=")">
              <date variable="issued">
                <date-part name="year"/>
              </date>
              <text variable="year-suffix"/>
              <choose>
                <if type="speech" match="any">
                  <date variable="issued">
                    <date-part prefix=", " name="month"/>
                  </date>
                </if>
                <else-if type="article-journal bill book chapter graphic legal_case legislation motion_picture paper-conference report song dataset" match="none">
                  <date variable="issued">
                    <date-part prefix=", " name="month"/>
                    <date-part prefix=" " name="day"/>
                  </date>
                </else-if>
              </choose>
            </group>
          </if>
          <else-if variable="status">
            <group prefix=" (" suffix=")">
              <text variable="status"/>
              <text variable="year-suffix" prefix="-"/>
            </group>
          </else-if>
          <else>
            <group prefix=" (" suffix=")">
              <text term="no date" form="short"/>
              <text variable="year-suffix" prefix="-"/>
            </group>
          </else>
        </choose>
      </if>
    </choose>
  </macro>
  <macro name="issued-sort">
    <choose>
      <if type="article-journal bill book chapter graphic legal_case legislation motion_picture paper-conference report song dataset" match="none">
        <date variable="issued">
          <date-part name="year"/>
          <date-part name="month"/>
          <date-part name="day"/>
        </date>
      </if>
      <else>
        <date variable="issued">
          <date-part name="year"/>
        </date>
      </else>
    </choose>
  </macro>
  <macro name="issued-year">
    <choose>
      <if variable="issued">
        <group delimiter="/">
          <date variable="original-date" form="text" date-parts="year"/>
          <group>
            <date variable="issued">
              <date-part name="year"/>
            </date>
            <text variable="year-suffix"/>
          </group>
        </group>
      </if>
      <else-if variable="status">
        <text variable="status"/>
        <text variable="year-suffix" prefix="-"/>
      </else-if>
      <else>
        <text term="no date" form="short"/>
        <text variable="year-suffix" prefix="-"/>
      </else>
    </choose>
  </macro>
  <macro name="edition">
    <choose>
      <if is-numeric="edition">
        <group delimiter=" ">
          <number variable="edition" form="ordinal"/>
          <text term="edition" form="short"/>
        </group>
      </if>
      <else>
        <text variable="edition"/>
      </else>
    </choose>
  </macro>
  <macro name="locators">
    <choose>
      <if type="article-journal article-magazine" match="any">
        <group prefix=", " delimiter=", ">
          <group>
            <text variable="volume" font-style="italic"/>
            <text variable="issue" prefix="(" suffix=")"/>
          </group>
          <text variable="page"/>
        </group>
      </if>
      <else-if type="article-newspaper">
        <group delimiter=" " prefix=", ">
          <label variable="page" form="short"/>
          <text variable="page"/>
        </group>
      </else-if>
      <else-if type="book graphic motion_picture report song chapter paper-conference entry-encyclopedia entry-dictionary" match="any">
        <group prefix=" (" suffix=")" delimiter=", ">
          <text macro="edition"/>
          <group>
            <text term="volume" form="short" plural="true" text-case="capitalize-first" suffix=" "/>
            <number variable="number-of-volumes" form="numeric" prefix="1&#8211;"/>
          </group>
          <group>
            <text term="volume" form="short" text-case="capitalize-first" suffix=" "/>
            <number variable="volume" form="numeric"/>
          </group>
          <group>
            <label variable="page" form="short" suffix=" "/>
            <text variable="page"/>
          </group>
        </group>
      </else-if>
      <else-if type="legal_case">
        <group prefix=" (" suffix=")" delimiter=" ">
          <text variable="authority"/>
          <date variable="issued" form="text"/>
        </group>
      </else-if>
      <else-if type="bill legislation" match="any">
        <date variable="issued" prefix=" (" suffix=")">
          <date-part name="year"/>
        </date>
      </else-if>
    </choose>
  </macro>
  <macro name="citation-locator">
    <group>
      <choose>
        <if locator="chapter">
          <label variable="locator" form="long" text-case="capitalize-first"/>
        </if>
        <else>
          <label variable="locator" form="short"/>
        </else>
      </choose>
      <text variable="locator" prefix=" "/>
    </group>
  </macro>
  <macro name="container">
    <choose>
      <if type="post-weblog webpage" match="none">
        <group>
          <choose>
            <if type="chapter paper-conference entry-encyclopedia" match="any">
              <text term="in" text-case="capitalize-first" suffix=" "/>
            </if>
          </choose>
          <text macro="container-contributors"/>
          <text macro="secondary-contributors"/>
          <text macro="container-title"/>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="container-title">
    <choose>
      <if type="article article-journal article-magazine article-newspaper" match="any">
        <text variable="container-title" font-style="italic" text-case="title"/>
      </if>
      <else-if type="bill legal_case legislation" match="none">
        <text variable="container-title" font-style="italic"/>
      </else-if>
    </choose>
  </macro>
  <macro name="legal-cites">
    <choose>
      <if type="bill legal_case legislation" match="any">
        <group prefix=", " delimiter=" ">
          <choose>
            <if variable="container-title">
              <text variable="volume"/>
              <text variable="container-title"/>
              <group delimiter=" ">
                <text term="section" form="symbol"/>
                <text variable="section"/>
              </group>
              <text variable="page"/>
            </if>
            <else>
              <choose>
                <if type="legal_case">
                  <text variable="number" prefix="No. "/>
                </if>
                <else>
                  <text variable="number" prefix="Pub. L. No. "/>
                  <group delimiter=" ">
                    <text term="section" form="symbol"/>
                    <text variable="section"/>
                  </group>
                </else>
              </choose>
            </else>
          </choose>
        </group>
      </if>
    </choose>
  </macro>
  <macro name="original-date">
    <choose>
      <if variable="original-date">
        <group prefix="(" suffix=")" delimiter=" ">
          <text value="Original work published"/>
          <date variable="original-date" form="text" date-parts="year"/>
        </group>
      </if>
    </choose>
  </macro>
  <citation et-al-min="3" et-al-use-first="1" et-al-subsequent-min="3" et-al-subsequent-use-first="1" disambiguate-add-year-suffix="true" disambiguate-add-names="true" disambiguate-add-givenname="true" collapse="year" givenname-disambiguation-rule="primary-name">
    <sort>
      <key macro="author"/>
      <key macro="issued-sort"/>
    </sort>
    <layout prefix="(" suffix=")" delimiter="; ">
      <group delimiter=", ">
        <text macro="author-short"/>
        <text macro="issued-year"/>
        <text macro="citation-locator"/>
      </group>
    </layout>
  </citation>
  <bibliography hanging-indent="true" et-al-min="8" et-al-use-first="6" et-al-use-last="true" entry-spacing="0" line-spacing="2">
    <sort>
      <key macro="author"/>
      <key macro="issued-sort" sort="ascending"/>
      <key macro="title"/>
    </sort>
    <layout>
      <group suffix=".">
        <group delimiter=". ">
          <text macro="author"/>
          <text macro="issued"/>
          <text macro="title-plus-extra"/>
          <text macro="container"/>
        </group>
        <text macro="legal-cites"/>
        <text macro="locators"/>
        <group delimiter=", " prefix=". ">
          <text macro="event"/>
          <text macro="publisher"/>
        </group>
      </group>
      <text macro="access" prefix=" "/>
      <text macro="original-date" prefix=" "/>
    </layout>
  </bibliography>
</style>
