# Simple script to generate PDF from R Markdown
# This script generates the final assignment PDF

# Load required packages
if (!require(rmarkdown)) {
  install.packages("rmarkdown")
}
if (!require(knitr)) {
  install.packages("knitr")
}

library(rmarkdown)
library(knitr)

cat("=== GENERATING ASSIGNMENT PDF ===\n")

# Check if authentication exists
token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")

if (!file.exists(token_path)) {
  cat("[ERROR] Authentication file not found\n")
  cat("Please run: auth_setup(name = 'account1') first\n")
  stop("Authentication required")
} else {
  cat("[SUCCESS] Authentication file found\n")
}

# Check if R Markdown file exists
if (!file.exists("Social_Media_Assignment.Rmd")) {
  cat("[ERROR] R Markdown file not found\n")
  stop("Social_Media_Assignment.Rmd not found")
} else {
  cat("[SUCCESS] R Markdown file found\n")
}

# Generate PDF
cat("\n=== GENERATING PDF ===\n")
cat("This may take several minutes as it collects real data from Mastodon...\n")

tryCatch({
  rmarkdown::render("Social_Media_Assignment.Rmd", 
                   output_format = "pdf_document",
                   output_file = "Social_Media_Assignment.pdf")
  cat("[SUCCESS] PDF generated successfully!\n")
  cat("  Output file: Social_Media_Assignment.pdf\n")
  
  if (file.exists("Social_Media_Assignment.pdf")) {
    cat("  File size:", file.size("Social_Media_Assignment.pdf"), "bytes\n")
  }
  
}, error = function(e) {
  cat("[ERROR] PDF generation failed:\n")
  cat("  Error:", e$message, "\n")
  
  # Try HTML as fallback
  cat("\n=== TRYING HTML FALLBACK ===\n")
  tryCatch({
    rmarkdown::render("Social_Media_Assignment.Rmd", 
                     output_format = "html_document",
                     output_file = "Social_Media_Assignment.html")
    cat("[SUCCESS] HTML generated as fallback!\n")
    cat("  Output file: Social_Media_Assignment.html\n")
  }, error = function(e2) {
    cat("[ERROR] HTML generation also failed:", e2$message, "\n")
  })
})

# Check for generated files
cat("\n=== GENERATED FILES ===\n")
output_files <- c("Social_Media_Assignment.pdf", 
                 "Social_Media_Assignment.html",
                 "mastodon_data.RData")

for (file in output_files) {
  if (file.exists(file)) {
    cat("[SUCCESS]", file, "created\n")
    cat("  Size:", file.size(file), "bytes\n")
  }
}

cat("\n=== ASSIGNMENT READY ===\n")
if (file.exists("Social_Media_Assignment.pdf")) {
  cat("Your assignment PDF is ready for submission!\n")
  cat("File: Social_Media_Assignment.pdf\n")
} else if (file.exists("Social_Media_Assignment.html")) {
  cat("Your assignment HTML is ready (PDF failed)!\n")
  cat("File: Social_Media_Assignment.html\n")
  cat("You can print this HTML to PDF if needed.\n")
} else {
  cat("Generation failed. Check error messages above.\n")
}
