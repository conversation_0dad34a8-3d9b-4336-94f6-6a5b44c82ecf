# Troubleshooting Guide - Social Media Assignment

## 🔧 Common Issues and Solutions

### Issue 1: "R Markdown shows no information"

**Problem**: The R Markdown document runs but doesn't show any data or analysis results.

**Causes & Solutions**:

1. **Authentication Issues**
   ```r
   # Check if authentication exists
   token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")
   file.exists(token_path)
   
   # If FALSE, run authentication again
   auth_setup(name = "account1")
   ```

2. **API Connection Problems**
   ```r
   # Test basic connection
   source("test_real_data.R")
   ```

3. **Package Issues**
   ```r
   # Reinstall rtoot package
   remove.packages("rtoot")
   install.packages("rtoot")
   library(rtoot)
   ```

### Issue 2: "Error in get_instance_general()"

**Problem**: API calls fail with connection errors.

**Solutions**:

1. **Check Internet Connection**
   - Ensure you have stable internet
   - Try accessing mastodon.social in browser

2. **Verify Authentication**
   ```r
   # Load and test token
   token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")
   token <- readRDS(token_path)
   print(token)
   ```

3. **Rate Limiting**
   - Wait 15 minutes between large API calls
   - Reduce limit parameters (e.g., limit = 20 instead of 100)

### Issue 3: "PDF Generation Fails"

**Problem**: R Markdown won't generate PDF output.

**Solutions**:

1. **Install LaTeX**
   ```r
   install.packages("tinytex")
   tinytex::install_tinytex()
   ```

2. **Use HTML Instead**
   ```r
   rmarkdown::render("Social_Media_Assignment.Rmd", 
                    output_format = "html_document")
   ```

3. **Check for Special Characters**
   - Ensure no special characters in file paths
   - Use English locale settings

### Issue 4: "Package Installation Errors"

**Problem**: Required packages won't install.

**Solutions**:

1. **Update R**
   - Install latest R version (4.4.2 or newer)
   - Restart R session

2. **Install Packages Individually**
   ```r
   install.packages("rtoot")
   install.packages("dplyr")
   install.packages("ggplot2")
   install.packages("lubridate")
   install.packages("tidytext")
   install.packages("wordcloud")
   install.packages("knitr")
   install.packages("rmarkdown")
   ```

3. **Use Different Repository**
   ```r
   install.packages("rtoot", repos = "https://cran.rstudio.com/")
   ```

### Issue 5: "Empty Data Frames"

**Problem**: API calls succeed but return empty data.

**Solutions**:

1. **Check Instance Status**
   - Verify mastodon.social is operational
   - Try different hashtags (e.g., "photography" instead of "rstats")

2. **Adjust Parameters**
   ```r
   # Try smaller limits
   public_timeline <- get_timeline_public(instance = "mastodon.social", limit = 20)
   
   # Try different hashtags
   posts <- get_timeline_hashtag(hashtag = "photography", 
                                instance = "mastodon.social", 
                                limit = 20)
   ```

3. **Check Time Periods**
   - Some data might be from different time periods
   - Instance activity covers last 3 months only

## 🚀 Quick Fixes

### Quick Test Sequence

1. **Test Authentication**
   ```r
   source("test_real_data.R")
   ```

2. **Test R Script**
   ```r
   source("Social_Media_Assignment.R")
   ```

3. **Test R Markdown**
   ```r
   source("test_rmarkdown.R")
   ```

### Emergency Fallback

If real data collection fails completely, you can still demonstrate the analysis framework:

```r
# Create minimal sample data for demonstration
instance_activity <- data.frame(
  week = seq(as.Date("2025-03-01"), as.Date("2025-05-25"), by = "week"),
  statuses = sample(900000:1100000, 12),
  logins = sample(150000:200000, 12),
  registrations = sample(10000:20000, 12)
)

# Continue with analysis...
```

## 📞 Getting Help

### Check These First

1. **File Locations**
   - Ensure all files are in the correct directory
   - Check working directory with `getwd()`

2. **R Session**
   - Restart R session if packages seem corrupted
   - Clear environment with `rm(list = ls())`

3. **Error Messages**
   - Read error messages carefully
   - Copy exact error text for troubleshooting

### Diagnostic Commands

```r
# Check R version
R.version.string

# Check package versions
packageVersion("rtoot")
packageVersion("rmarkdown")

# Check working directory
getwd()

# List files
list.files()

# Check authentication
file.exists(file.path(tools::R_user_dir("rtoot", "config"), "account1.rds"))
```

### Alternative Approaches

1. **Use RStudio Cloud**
   - Upload files to RStudio Cloud
   - Run analysis in cloud environment

2. **Simplify Analysis**
   - Comment out complex visualizations
   - Focus on basic data collection and summary statistics

3. **Manual Data Entry**
   - If API fails, manually enter sample data
   - Document the limitation in your report

## ✅ Success Indicators

You know everything is working when:

- ✅ `test_real_data.R` shows all green checkmarks
- ✅ `Social_Media_Assignment.R` runs without errors
- ✅ R Markdown generates HTML or PDF output
- ✅ You see actual Mastodon data in the results
- ✅ Plots and tables display correctly

## 📝 Final Notes

- **Internet Required**: All real data collection requires internet connection
- **Rate Limits**: Mastodon API has rate limits - be patient
- **Authentication**: Keep your token secure and don't share it
- **Backup**: Save your work frequently during analysis

If you continue to have issues, the assignment framework is solid and demonstrates your understanding of social media analytics principles, even if some real-time data collection encounters technical difficulties.
