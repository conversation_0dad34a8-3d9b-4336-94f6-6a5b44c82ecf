# Test script to verify real data collection from Mastodon API
# This script tests the authentication and basic data collection

# Load required packages
library(rtoot)
library(dplyr)

cat("=== TESTING REAL DATA COLLECTION ===\n")

# Check authentication
token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")

if (file.exists(token_path)) {
  cat("[SUCCESS] Authentication file found\n")
  token <- readRDS(token_path)
  options("rtoot_token" = token_path)
  cat("[SUCCESS] Token loaded successfully\n")
} else {
  cat("[ERROR] Authentication file not found\n")
  cat("Please run: auth_setup(name = 'account1')\n")
  stop("Authentication required")
}

# Test basic API calls
cat("\n=== TESTING API CALLS ===\n")

# Test 1: Instance information
cat("Testing instance information...\n")
tryCatch({
  instance_info <- get_instance_general(instance = "mastodon.social")
  cat("[SUCCESS] Instance info retrieved successfully\n")
  cat("  Instance title:", instance_info$title, "\n")
  cat("  User count:", instance_info$stats$user_count, "\n")
}, error = function(e) {
  cat("[ERROR] Instance info failed:", e$message, "\n")
})

# Test 2: Instance activity
cat("\nTesting instance activity...\n")
tryCatch({
  instance_activity <- get_instance_activity(instance = "mastodon.social")
  cat("[SUCCESS] Instance activity retrieved successfully\n")
  cat("  Records:", nrow(instance_activity), "\n")
  if (nrow(instance_activity) > 0) {
    cat("  Latest week posts:", instance_activity$statuses[1], "\n")
  }
}, error = function(e) {
  cat("[ERROR] Instance activity failed:", e$message, "\n")
})

# Test 3: Trending hashtags
cat("\nTesting trending hashtags...\n")
tryCatch({
  instance_trends <- get_instance_trends(instance = "mastodon.social")
  cat("[SUCCESS] Trending hashtags retrieved successfully\n")
  cat("  Number of trends:", nrow(instance_trends), "\n")
  if (nrow(instance_trends) > 0) {
    cat("  Top hashtag:", instance_trends$name[1], "with", instance_trends$uses[1], "uses\n")
  }
}, error = function(e) {
  cat("[ERROR] Trending hashtags failed:", e$message, "\n")
})

# Test 4: Public timeline (small sample)
cat("\nTesting public timeline...\n")
tryCatch({
  public_timeline <- get_timeline_public(instance = "mastodon.social", limit = 10)
  cat("[SUCCESS] Public timeline retrieved successfully\n")
  cat("  Posts retrieved:", nrow(public_timeline), "\n")
  if (nrow(public_timeline) > 0) {
    cat("  Latest post ID:", public_timeline$id[1], "\n")
  }
}, error = function(e) {
  cat("[ERROR] Public timeline failed:", e$message, "\n")
})

# Test 5: Hashtag timeline (small sample)
cat("\nTesting hashtag timeline (#rstats)...\n")
tryCatch({
  rstats_posts <- get_timeline_hashtag(hashtag = "rstats",
                                      instance = "mastodon.social",
                                      limit = 10)
  cat("[SUCCESS] #rstats posts retrieved successfully\n")
  cat("  Posts retrieved:", nrow(rstats_posts), "\n")
  if (nrow(rstats_posts) > 0) {
    cat("  Latest #rstats post ID:", rstats_posts$id[1], "\n")
  }
}, error = function(e) {
  cat("[ERROR] #rstats posts failed:", e$message, "\n")
})

cat("\n=== TEST COMPLETED ===\n")
cat("If all tests passed, you can run the full assignment!\n")
cat("Use: source('Social_Media_Assignment.R')\n")
cat("Or generate PDF: rmarkdown::render('Social_Media_Assignment.Rmd')\n")
