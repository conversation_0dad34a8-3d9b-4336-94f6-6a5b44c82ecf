# Script to run the Social Media Assignment
# Western Sydney University MBA - Quarter 2:2025

# Set working directory
setwd("Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Assignment")

# Install required packages if not already installed
required_packages <- c("rtoot", "dplyr", "ggplot2", "lubridate", "tidytext", 
                      "wordcloud", "knitr", "rmarkdown", "tidyr")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# Option 1: Run the R script directly
cat("Running Social Media Assignment R Script...\n")
source("Social_Media_Assignment.R")

# Option 2: Knit the R Markdown to PDF (requires authentication setup)
cat("\nTo generate PDF report, run the following command after setting up authentication:\n")
cat("rmarkdown::render('Social_Media_Assignment.Rmd', output_format = 'pdf_document')\n")

# Option 3: Knit to HTML (alternative if PDF has issues)
cat("\nTo generate HTML report:\n")
cat("rmarkdown::render('Social_Media_Assignment.Rmd', output_format = 'html_document')\n")

# Instructions for authentication
cat("\n" + paste(rep("=", 60), collapse = "") + "\n")
cat("AUTHENTICATION SETUP INSTRUCTIONS\n")
cat(paste(rep("=", 60), collapse = "") + "\n")
cat("1. Run: auth_setup(name = 'account1')\n")
cat("2. Follow browser prompts to authenticate with Mastodon\n")
cat("3. Copy the authorization code back to R\n")
cat("4. Run the analysis scripts\n")
cat("\nNote: You already have authentication set up from your terminal session!\n")

# Check if authentication exists
auth_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")
if (file.exists(auth_path)) {
  cat("\n✓ Authentication file found at:", auth_path, "\n")
  cat("You can proceed with running the analysis!\n")
} else {
  cat("\n⚠ Authentication file not found. Please run auth_setup() first.\n")
}

cat("\nAssignment files created:\n")
cat("- Social_Media_Assignment.R (Complete R script)\n")
cat("- Social_Media_Assignment.Rmd (R Markdown for PDF/HTML output)\n")
cat("- run_assignment.R (This helper script)\n")
