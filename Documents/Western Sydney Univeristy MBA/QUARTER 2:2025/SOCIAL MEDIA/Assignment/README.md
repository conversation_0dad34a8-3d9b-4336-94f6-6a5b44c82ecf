# Social Media Analytics Assignment - Mastodon Analysis

**Western Sydney University MBA - Quarter 2:2025**  
**Course: Social Media Analytics**

## Overview

This assignment analyzes social media data from Mastodon, a decentralized social media platform, using the `rtoot` package in R. The analysis demonstrates comprehensive social media analytics techniques including:

- Instance activity pattern analysis
- Content engagement metrics
- Hashtag trend analysis
- Text analysis and sentiment
- Comparative analysis between content types
- Strategic insights for social media marketing

## Files Included

1. **`Social_Media_Assignment.R`** - Complete R script with all analysis code
2. **`Social_Media_Assignment.Rmd`** - R Markdown document for generating PDF/HTML reports
3. **`run_assignment.R`** - Helper script to run the analysis
4. **`README.md`** - This instruction file
5. **`Assignment test.R`** - Original test file (updated with comprehensive analysis)

## Prerequisites

### Required R Packages
```r
install.packages(c("rtoot", "dplyr", "ggplot2", "lubridate", 
                   "tidytext", "wordcloud", "knitr", "rmarkdown", "tidyr"))
```

### System Requirements
- R version 4.0 or higher
- RStudio (recommended)
- LaTeX distribution (for PDF output) - install tinytex if needed:
  ```r
  install.packages("tinytex")
  tinytex::install_tinytex()
  ```

## Setup Instructions

### 1. Authentication Setup
Since you already have authentication set up from your terminal session, you can skip this step. If needed:

```r
library(rtoot)
auth_setup(name = "account1")
```

Follow the browser prompts to authenticate with mastodon.social.

### 2. Running the Analysis

#### Option A: Run R Script Directly
```r
source("Social_Media_Assignment.R")
```

#### Option B: Generate PDF Report
```r
rmarkdown::render("Social_Media_Assignment.Rmd", output_format = "pdf_document")
```

#### Option C: Generate HTML Report
```r
rmarkdown::render("Social_Media_Assignment.Rmd", output_format = "html_document")
```

#### Option D: Use Helper Script
```r
source("run_assignment.R")
```

## Analysis Components

### 1. Instance Analysis
- **Activity Trends**: 3-month historical data of posts, logins, and registrations
- **Growth Patterns**: Weekly activity analysis with visualizations
- **Summary Statistics**: Key performance indicators

### 2. Content Analysis
- **Public Timeline**: Sample of 100 recent public posts
- **Hashtag Analysis**: Trending hashtags and their usage patterns
- **Posting Patterns**: Temporal analysis of posting behavior

### 3. Engagement Metrics
- **Interaction Analysis**: Reblogs, favorites, and replies
- **Engagement Distribution**: Statistical analysis of engagement patterns
- **Comparative Analysis**: General posts vs. specialized content (#rstats)

### 4. Text Analysis
- **Word Frequency**: Most common words in #rstats posts
- **Content Cleaning**: HTML tag removal, URL filtering
- **Visualization**: Word clouds and frequency plots

### 5. Strategic Insights
- **Platform Comparison**: Decentralized vs. centralized social media
- **Community Behavior**: Technical community engagement patterns
- **Recommendations**: Strategic implications for social media marketing

## Key Findings

Based on the analysis, the assignment reveals:

1. **Decentralized Engagement**: Different patterns compared to traditional platforms
2. **Community Focus**: Specialized communities show higher engagement
3. **Quality over Quantity**: Meaningful interactions drive engagement
4. **Growth Trends**: Consistent platform growth and user adoption

## Output Files

After running the analysis, you'll generate:

- **PDF Report**: `Social_Media_Assignment.pdf` (comprehensive formatted report)
- **HTML Report**: `Social_Media_Assignment.html` (web-viewable version)
- **Plots**: Various visualization files (if saved separately)
- **Data**: Processed datasets for further analysis

## Troubleshooting

### Common Issues

1. **Authentication Error**
   - Ensure you've run `auth_setup()` successfully
   - Check that the token file exists in the rtoot config directory

2. **Package Installation Issues**
   - Update R to the latest version
   - Install packages one by one if bulk installation fails

3. **PDF Generation Issues**
   - Install tinytex: `tinytex::install_tinytex()`
   - Use HTML output as alternative

4. **API Rate Limits**
   - The analysis uses reasonable limits (100 posts per category)
   - If you encounter rate limits, reduce the `limit` parameters

### Data Limitations

- **Sample Size**: Analysis uses limited samples for demonstration
- **Time Period**: Covers specific time periods (adjust dates as needed)
- **Instance Focus**: Primarily analyzes mastodon.social
- **Real-time Data**: Results reflect data at time of analysis

## Academic Integrity

This assignment demonstrates:
- **Original Analysis**: Custom R code for Mastodon data analysis
- **Proper Citations**: References to rtoot package and methodology
- **Reproducible Research**: Complete code and documentation provided
- **Critical Thinking**: Strategic insights and recommendations

## Technical Notes

### rtoot Package Features Used
- `auth_setup()`: Authentication management
- `get_instance_general()`: Instance information
- `get_instance_activity()`: Historical activity data
- `get_instance_trends()`: Trending hashtags
- `get_timeline_public()`: Public timeline posts
- `get_timeline_hashtag()`: Hashtag-specific content

### Analysis Techniques
- **Data Visualization**: ggplot2 for professional charts
- **Text Processing**: tidytext for natural language processing
- **Statistical Analysis**: Summary statistics and comparative analysis
- **Report Generation**: R Markdown for professional documentation

## Submission Guidelines

For assignment submission, include:

1. **PDF Report**: Generated from R Markdown
2. **Source Code**: All R files (`.R` and `.Rmd`)
3. **Documentation**: This README file
4. **Data Files**: Any saved datasets (if required)

## Contact and Support

For technical issues with the rtoot package:
- Package documentation: https://cran.r-project.org/web/packages/rtoot/
- Mastodon API documentation: https://docs.joinmastodon.org/api/

---

**Assignment completed using rtoot package for R**  
**Western Sydney University MBA - Social Media Analytics**  
**Date: May 25, 2025**
