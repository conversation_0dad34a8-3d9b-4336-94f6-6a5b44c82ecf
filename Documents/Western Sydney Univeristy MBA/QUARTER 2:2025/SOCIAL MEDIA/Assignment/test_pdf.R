# Test script to generate PDF from R Markdown
# This script tests if the PDF generation works properly

# Install required packages
if (!require(rmarkdown)) {
  install.packages("rmarkdown")
}
if (!require(knitr)) {
  install.packages("knitr")
}

library(rmarkdown)
library(knitr)

# Set working directory
setwd("Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Assignment")

# Check if files exist
cat("Checking files...\n")
files_to_check <- c("Social_Media_Assignment.Rmd", "Social_Media_Assignment.R")
for (file in files_to_check) {
  if (file.exists(file)) {
    cat("✓", file, "exists\n")
  } else {
    cat("✗", file, "missing\n")
  }
}

# Try to render to HTML first (easier than PDF)
cat("\nAttempting to render HTML report...\n")
tryCatch({
  rmarkdown::render("Social_Media_Assignment.Rmd", 
                   output_format = "html_document",
                   output_file = "Social_Media_Assignment.html")
  cat("✓ HTML report generated successfully!\n")
}, error = function(e) {
  cat("✗ HTML generation failed:", e$message, "\n")
})

# Try to render to PDF
cat("\nAttempting to render PDF report...\n")
tryCatch({
  rmarkdown::render("Social_Media_Assignment.Rmd", 
                   output_format = "pdf_document",
                   output_file = "Social_Media_Assignment.pdf")
  cat("✓ PDF report generated successfully!\n")
}, error = function(e) {
  cat("✗ PDF generation failed:", e$message, "\n")
  cat("Note: PDF generation requires LaTeX. Try installing tinytex:\n")
  cat("install.packages('tinytex')\n")
  cat("tinytex::install_tinytex()\n")
})

# List generated files
cat("\nGenerated files:\n")
output_files <- c("Social_Media_Assignment.html", "Social_Media_Assignment.pdf")
for (file in output_files) {
  if (file.exists(file)) {
    cat("✓", file, "created\n")
  }
}

cat("\nTest completed!\n")
